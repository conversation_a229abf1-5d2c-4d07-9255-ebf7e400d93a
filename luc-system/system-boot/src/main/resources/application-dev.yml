spring:
  # mysql
  datasource:
    # 只使用druid时的数据库配置
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: luc123456
    url: ************************************************************************************************************************

    # 使用dynamic datasource的数据库配置
  #    dynamic:
  #      primary: master
  #      aop:
  #        expression-enabled: true
  #      druid:
  #        not-full-timeout-retry-count: 0
  #        connection-error-retry-attempts: 1
  #        time-between-connect-error-millis: 6000
  #        time-between-eviction-runs-millis: 6000
  #        break-after-acquire-failure: false
  #      datasource:
  #        master:
  #          url: *************************************************************************************************************************************************************
  #          username: root
  #          password: luc123456
  #          driver-class-name: com.mysql.cj.jdbc.Driver
  #          pointcut: 'execution(* com.lc.system.service.TestService.*Master(..))'
  #        slave:
  #          username: root
  #          password: luc123456
  #          url: ***********************************************************************************************************************************************
  #        shardingSphere:
  #          sharding: true
  #          sharding-config: config/sharding-sphere.yml

  data:
    # redis
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      timeout: 5000
      username:
      password: luc123456
      client-type: lettuce
      lettuce:
        pool:
          min-idle: 8
          max-idle: 500
          max-active: 2000
          max-wait: 10000
          enabled: true
  jackson:
    serialization:
      fail-on-empty-beans: false

# mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      logic-delete-field: status
      logic-delete-value: 1
      logic-not-delete-value: 0

# framework-datascope数据权限配置
data-scope:
  enabled: true
  ## 支持两种方式定义DataScopeSqlHandler
  handler-definition:
    ## 1、 配置文件声明bean，且声明属性
    - id: SysUser
      support-tables:
        - database: auth_center
          table-name: sys_user
          column-name: user_id
          column-type: varchar
        - database: auth_center
          table-name: sys_role
          column-name: role_id
          column-type: varchar
    ## 2、配置文件只声明bean的创建，使用DataScopeSqlHandlerCustomizer在代码中对bean进行定制化开发
    - id: Tenant



# framework-apidoc 日志设置，后续迁移到nacos中，各服务使用share-configs引入springdoc:
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    show-extensions: true
  #    default-model-expand-depth:
  #    enabled: false
  api-docs:
    path: /v3/api-docs
  #    enabled: false
  group-configs:
    - group: "default"
      paths-to-match: '/**'
      packages-to-scan: com.lc
  default-flat-param-object: true
  # OAuth2 配置 - 指向网关地址
  info:
    title: "系统服务API文档"
    version: "1.0.0"
    description: "系统用户权限管理相关接口"
    # 关键配置：通过网关访问认证服务器
    authorization-url: http://localhost:8809/auth-server/oauth2/authorize
    token-uri: http://localhost:8809/auth-server/oauth2/token


# 存储工具配置示例
file:
  storage:
    enabled: true
    oss:
      enabled: true
      # 存储平台
      platform: qiniu
      # ak
      access-key: lUIjHxV60do6tsmas9ZW2FRDGh_Vs0o9YINgYbed
      # sk
      secret-key: X9jo5EcdPYgZ0os8Z-h2DFf0gMihI773_6hP3whz
      # 默认bucket名
      default-bucket-name: mini-program-cookbook
      # 默认domain
      default-domain-of-bucket: qiniucs.com
      use-async: true
      cdn: storage.cloud.ffluc.online
      buckets:
        - name: mini-program-cookbook
          region: cn-south-1
          use-https: true
          path-style-enabled: false