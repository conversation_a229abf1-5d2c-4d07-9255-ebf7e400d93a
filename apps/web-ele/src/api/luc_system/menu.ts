import type { RouteRecordStringComponent } from '@vben/types';

import { lucSystemPrefix, requestClient } from '#/api/request';

export namespace MenuApi {
  export interface MenuInfoVO {
    id: number;
    menuId: string;
    parentMenuId?: string;
    name: string;
    path: string;
    component: string;
    redirect: string;
    menuType: string;
    sortOrder: number;
    status: number;
    dtCreated: Date;
    meta: MenuApi.MenuMetaVO;
    children: MenuApi.MenuInfoVO[];
  }

  export interface MenuMetaVO {
    title: string;
    icon: string;
    activeIcon: string;
    activePath: string;
    authority: string;
    link: string;
  }
}

/**
 * 获取用户可访问的菜单树
 * 用户信息由网关从 token 中解析并传递给后端
 */
export async function getAvailableMenuTree() {
  return requestClient.get<RouteRecordStringComponent[]>(
    `${lucSystemPrefix}/menu/tree/available`,
  );
}

/**
 * 获取菜单列表
 * 用户信息由网关从 token 中解析并传递给后端
 */
export async function getMenuList() {
  return requestClient.get<Array<MenuApi.MenuInfoVO>>(
    `${lucSystemPrefix}/menu/list`,
  );
}

export async function saveMenu(data: MenuApi.MenuInfoVO) {
  return requestClient.post<MenuApi.MenuInfoVO>(
    `${lucSystemPrefix}/menu/save`,
    data,
  );
}

export async function deleteMenu(menuId: string) {
  return requestClient.post<string>(
    `${lucSystemPrefix}/menu/delete`,
    { menuId },
    true,
  );
}
