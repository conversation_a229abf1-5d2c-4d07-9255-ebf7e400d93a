<script lang="ts" setup>
import type { MenuApi } from '#/api';

import { ref } from 'vue';

import { useVbenForm, useVbenModal, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { ElInput, ElMessage } from 'element-plus';

import { deleteMenu, saveMenu } from '#/api';

// 驼峰转横线分隔的工具函数
function camelToKebab(str: string): string {
  return str.replaceAll(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

// 定义操作类型
type ActionType = 'append' | 'create' | 'delete' | 'edit' | 'view';

// 定义操作API映射
const actionApis = {
  append: (data: MenuApi.MenuInfoVO) => saveMenu(data),
  create: (data: MenuApi.MenuInfoVO) => saveMenu(data),
  delete: (data: MenuApi.MenuInfoVO) => deleteMenu(data.menuId),
  edit: (data: MenuApi.MenuInfoVO) => saveMenu(data),
};
const formData = ref<MenuApi.MenuInfoVO>();
// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    // 设置标签宽度
    labelWidth: 100,
    // 添加冒号
    colon: true,
  },
  // 水平布局，标签和输入框在同一行，便于对齐
  layout: 'horizontal',
  showDefaultActions: false,
  // 使用简单的网格布局
  wrapperClass: 'grid grid-cols-1 lg:grid-cols-2',
  schema: [
    {
      label: $t('form.menu.header.menuType'),
      fieldName: 'menuType',
      component: 'RadioGroup',
      formItemClass: 'col-span-2 md:col-span-2',
      defaultValue: 'menu',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.menuType'),
        isButton: true,
        options: [
          { label: $t('constants.menu.type.catalog'), value: 'catalog' },
          { label: $t('constants.menu.type.menu'), value: 'menu' },
          { label: $t('constants.menu.type.button'), value: 'button' },
          { label: $t('constants.menu.type.iframe'), value: 'embedded' },
          { label: $t('constants.menu.type.link'), value: 'link' },
        ],
      },
    },
    {
      label: $t('form.menu.header.menuId'),
      fieldName: 'menuId',
      component: 'Input',
      componentProps: {
        disabled: true,
        placeholder: $t('form.menu.placeHolder.menuId'),
      },
      dependencies: {
        triggerFields: ['menuType'],
        show: (value) => value.menuType !== 'button',
        rules: (value) =>
          z
            .string()
            .default(
              value.menuId ??
                (value.menuName ? camelToKebab(value.menuName) : '默认id'),
            ),
      },
      rules: 'required',
    },
    {
      label: $t('form.menu.header.status'),
      fieldName: 'status',
      component: 'Select',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.status'),
        options: [
          { label: $t('constants.status.disabled'), value: 0 },
          { label: $t('constants.status.enabled'), value: 1 },
        ],
      },
    },
    {
      label: $t('form.menu.header.meta.title'),
      fieldName: 'meta.title',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.meta.title'),
      },
      rules: 'required',
    },
    {
      label: $t('form.menu.header.parentMenuId'),
      fieldName: 'parentMenuId',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.parentMenuId'),
      },
      dependencies: {
        triggerFields: ['parentMenuId'],
        // 新增下级菜单操作时，不允许编辑父菜单
        disabled: () => getActionType() === 'append',
      },
    },
    {
      label: $t('form.menu.header.name'),
      fieldName: 'name',
      component: 'Input',
      dependencies: {
        triggerFields: ['name'],
        disabled: () => getActionType() !== 'create',
      },
      componentProps: {
        // 如果表单数据中已有路由名称，则禁用编辑
        placeholder: $t('form.menu.placeHolder.name'),
        onChange: async (nameValue: string) => {
          // 如果menuId字段为空或者是自动生成的，则自动更新
          const currentValues = await formApi.getValues();
          if (!currentValues.menuId && !formData.value?.menuId) {
            const generatedMenuId = camelToKebab(nameValue);
            formApi.setFieldValue('menuId', generatedMenuId);
          }
        },
      },
      rules: 'required',
    },
    {
      label: $t('form.menu.header.path'),
      fieldName: 'path',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.path'),
      },
      dependencies: {
        triggerFields: ['menuType'],
        show: (values) =>
          ['catalog', 'iframe', 'menu'].includes(values.menuType),
      },
      rules: 'required',
    },
    {
      label: $t('form.menu.header.component'),
      fieldName: 'component',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.component'),
      },
      dependencies: {
        triggerFields: ['menuType'],
        show: (values) => values.menuType === 'menu',
      },
      rules: z
        .string()
        .refine((value) => value.startsWith('/'), $t(''))
        .refine((value) => value && getActionType() !== 'delete', $t('')),
    },
    {
      label: $t('form.menu.header.redirect'),
      fieldName: 'redirect',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.redirect'),
      },
    },
    {
      label: $t('form.menu.header.sortOrder'),
      fieldName: 'sortOrder',
      component: 'InputNumber',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.sortOrder'),
      },
      rules: z.number().int().min(-1).max(9999).default(-1),
    },
    {
      label: $t('form.menu.header.meta.icon'),
      fieldName: 'meta.icon',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.meta.icon'),
      },
    },
    {
      label: $t('form.menu.header.meta.activeIcon'),
      fieldName: 'meta.activeIcon',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.meta.activeIcon'),
      },
    },
    {
      label: $t('form.menu.header.meta.activePath'),
      fieldName: 'meta.activePath',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.meta.activePath'),
      },
    },
    {
      label: $t('form.menu.header.meta.authority'),
      fieldName: 'meta.authority',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.meta.authority'),
      },
    },
    {
      label: $t('form.menu.header.meta.link'),
      fieldName: 'meta.link',
      component: 'Input',
      componentProps: {
        placeholder: $t('form.menu.placeHolder.meta.link'),
      },
    },
  ],
});

// 获取当前操作类型
function getActionType(): ActionType {
  return modalApi.getData<{ actionType: ActionType }>()?.actionType || 'view';
}

// 模态框配置
const [Modal, modalApi] = useVbenModal({
  // 启用拖拽功能
  draggable: true,
  // 显示全屏按钮
  fullscreenButton: true,
  // 居中显示
  centered: true,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 打开弹窗时，渲染表单数据
      const { actionType, row } = modalApi.getData<{
        actionType: ActionType;
        row?: MenuApi.MenuInfoVO;
      }>();

      if (row) {
        // 设置表单值
        formData.value = row;
        formApi.setValues(row);
        // 根据操作类型设置表单状态
        if (actionType === 'view') {
          // 查看模式：禁用所有字段
          formApi.setState({
            commonConfig: {
              disabled: true,
            },
          });
        } else {
          // 编辑/新增模式：启用所有字段
          formApi.setState({
            commonConfig: {
              disabled: false,
            },
          });
        }
      } else {
        // 重置表单
        formApi.resetForm();
      }
    }
  },

  async onConfirm() {
    const data = modalApi.getData<{
      actionType: ActionType;
      formData?: MenuApi.MenuInfoVO;
      originalRow?: MenuApi.MenuInfoVO;
    }>();

    // 表单验证
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    try {
      modalApi.lock();
      // 确保actionType是actionApis的有效键，并且formData存在
      if (data?.actionType && data.actionType in actionApis && data.formData) {
        await actionApis[data.actionType as keyof typeof actionApis](
          data.formData,
        );
        ElMessage.success('操作成功');
      } else {
        ElMessage.error('输入有误');
      }
    } catch {
      ElMessage.error('操作失败');
    } finally {
      modalApi.unlock();
      modalApi.close();
    }
  },
});
</script>

<template>
  <Modal class="w-[1000px] max-w-[95vw]">
    <!-- 删除确认模式 -->
    <div v-if="getActionType() === 'delete'" class="p-8">
      <div class="flex flex-col items-center space-y-6">
        <div
          class="flex h-16 w-16 items-center justify-center rounded-full bg-red-100"
        >
          <svg
            class="h-8 w-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <div class="space-y-2 text-center">
          <h3 class="text-xl font-semibold text-gray-900">确认删除菜单</h3>
          <p class="text-gray-600">
            您即将删除菜单
            <span class="font-medium text-red-600">{{
              modalApi.getData()?.formData?.name ||
              modalApi.getData()?.row?.name
            }}</span>
          </p>
          <p class="text-sm text-gray-500">此操作不可恢复，请谨慎操作</p>
        </div>
      </div>
    </div>

    <!-- 表单模式 -->
    <div v-else class="p-6">
      <Form>
        <!-- 设置标题后缀slot -->
        <template #[`meta.title`]="schemaProps">
          <ElInput v-bind="schemaProps">
            <template #suffix>{{ $t(schemaProps.field.value) }}</template>
          </ElInput>
        </template>
        <!-- 设置父菜单后缀slot -->
        <template #parentMenuId="schemaProps">
          <ElInput v-bind="schemaProps">
            <template #suffix>{{ schemaProps.field }}</template>
          </ElInput>
        </template>
      </Form>
    </div>
  </Modal>
</template>
