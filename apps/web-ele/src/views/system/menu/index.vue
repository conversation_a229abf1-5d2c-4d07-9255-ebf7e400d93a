<!-- eslint-disable perfectionist/sort-imports -->
<script lang="tsx" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ElButton } from 'element-plus';
import { getMenuList } from '#/api';
import type { MenuApi } from '#/api';
import { $t } from '@vben/locales';
import { IconifyIcon } from '@vben/icons';
import MenuForm from './module/menuForm.vue';

defineOptions({
  name: 'MenuManage',
});

// 表格格式配置
const gridOptions: VxeTableGridOptions<MenuApi.MenuInfoVO> = {
  columns: [
    {
      field: 'meta.title',
      slots: { default: 'title' },
      formatter: ({ cellValue }) => $t(`${cellValue}`),
      treeNode: true,
      title: $t('form.menu.header.title'),
      width: 150,
    },
    { field: 'name', title: `${$t('form.menu.header.name')}`, width: 150 },
    { field: 'path', title: `${$t('form.menu.header.path')}`, width: 200 },
    {
      field: 'menuType',
      title: `${$t('form.menu.header.menuType')}`,
      cellRender: {
        name: 'CellTag',
        options: [
          {
            value: 'catalog',
            type: 'info',
            label: $t('constants.menu.type.catalog'),
          },
          {
            value: 'menu',
            type: 'primary',
            label: $t('constants.menu.type.menu'),
          },
          {
            value: 'button',
            type: 'danger',
            label: $t('constants.menu.type.button'),
          },
          {
            value: 'iframe',
            type: 'success',
            label: $t('constants.menu.type.iframe'),
          },
          {
            value: 'link',
            type: 'warning',
            label: $t('constants.menu.type.link'),
          },
        ],
      },
      width: 100,
    },
    { field: 'status', title: `${$t('form.menu.header.status')}`, width: 80 },
    {
      field: 'operation',
      fixed: 'right',
      title: `${$t('form.menu.header.operation.title')}`,
      width: 200,
      cellRender: {
        name: 'CellVbenButtonGroup',
        props: { gap: 10 },
        options: [
          {
            text: `${$t('form.menu.header.operation.edit')}`,
            variant: 'primary',
            handler: (row: MenuApi.MenuInfoVO) => onEdit(row),
          },
          {
            text: `${$t('form.menu.header.operation.append')}`,
            variant: 'info',
            handler: (row: MenuApi.MenuInfoVO) => onAppend(row),
          },
          {
            text: `${$t('form.menu.header.operation.delete')}`,
            variant: 'destructive',
            handler: (row: MenuApi.MenuInfoVO) => onDelete(row),
          },
        ],
      },
    },
  ],
  rowConfig: { keyField: 'id' },
  proxyConfig: {
    ajax: {
      query: () => getMenuList(),
    },
    response: {
      list: '',
    },
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  treeConfig: {
    parentField: 'parentMenuId',
    rowField: 'menuId',
    // 如果后端返回的是扁平数据，设置为 true；如果已经是树形数据，设置为 false
    transform: true,
  },
};
// 表格点击事件配置
const gridEvents: VxeGridListeners<MenuApi.MenuInfoVO> = {
  cellClick: ({ row, column }: { column: any; row: MenuApi.MenuInfoVO }) => {
    if (column.title === $t('form.menu.header.title')) {
      onView(row);
    }
  },
};

// 创建菜单表格
const [Grid] = useVbenVxeGrid({ gridOptions, gridEvents });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: MenuForm,
  destroyOnClose: true,
});

function onView(row: MenuApi.MenuInfoVO) {
  formModalApi
    .setData({
      actionType: 'view',
      row,
    })
    .open();
}
// 创建菜单
function onCreate() {
  formModalApi.setData({ actionType: 'create' }).open();
}
// 编辑菜单
function onEdit(row: MenuApi.MenuInfoVO) {
  formModalApi
    .setData({
      actionType: 'edit',
      row,
    })
    .open();
}
// 删除菜单
function onDelete(row: MenuApi.MenuInfoVO) {
  formModalApi
    .setData({
      actionType: 'delete',
      row: { id: row.id, menuId: row.menuId },
    })
    .open();
}
// 新增下级菜单
function onAppend(row: MenuApi.MenuInfoVO) {
  formModalApi
    .setData({
      actionType: 'append',
      row: { parentMenuId: row.menuId },
    })
    .open();
}
</script>
<template>
  <Page description="page.system.menu" title="菜单管理" auto-content-height>
    <div class="mb-4">
      <ElButton type="primary" @click="onCreate">新增菜单</ElButton>
    </div>
    <Grid>
      <template #title="{ row }">
        <div class="flex w-full items-center gap-1">
          <div class="size-5 flex-shrink-0">
            <IconifyIcon
              v-if="row.menuType === 'button'"
              icon="carbon:security"
              class="size-full"
            />
            <IconifyIcon
              v-else-if="row.meta?.icon"
              :icon="row.meta?.icon || 'carbon:circle-dash'"
              class="size-full"
            />
          </div>
          <span class="flex-auto">{{ $t(row.meta?.title) }}</span>
          <div class="items-center justify-end"></div>
        </div>
      </template>
    </Grid>
    <FormModal />
  </Page>
</template>
